name = "snowball-monitor"
main = "workers/src/index.ts"
compatibility_date = "2024-11-01"

# Bind your KV namespace. Replace the id/preview_id after creating the namespace via:
#   wrangler kv:namespace create STATE_KV
#   wrangler kv:namespace create STATE_KV --preview
[[kv_namespaces]]
binding = "STATE_KV"
id = "4e69b4e41c764083bfc017788591d8c2"
preview_id = "4e69b4e41c764083bfc017788591d8c2"

[triggers]
# Default: run every minute
crons = ["* * * * *"]

[vars]
# Optional fallback if KV config not present
TZ = "Asia/Shanghai"
# Optional fallback comma-separated list of cube ids
# CUBE_IDS = "ZH3334492,ZHxxxxxxx"

